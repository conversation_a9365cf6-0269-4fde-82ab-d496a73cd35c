<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>1200</width>
    <height>700</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>材料数据库管理系统</string>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QHBoxLayout" name="main_layout" stretch="">
    <property name="spacing">
     <number>15</number>
    </property>
    <property name="leftMargin">
     <number>15</number>
    </property>
    <property name="topMargin">
     <number>15</number>
    </property>
    <property name="rightMargin">
     <number>15</number>
    </property>
    <property name="bottomMargin">
     <number>15</number>
    </property>
    <item>
     <widget class="QFrame" name="left_frame">
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <layout class="QVBoxLayout" name="left_layout">
       <property name="spacing">
        <number>15</number>
       </property>
       <property name="leftMargin">
        <number>15</number>
       </property>
       <property name="topMargin">
        <number>15</number>
       </property>
       <property name="rightMargin">
        <number>15</number>
       </property>
       <property name="bottomMargin">
        <number>15</number>
       </property>
       <item>
        <widget class="QLabel" name="title_label">
         <property name="text">
          <string>材料管理</string>
         </property>
         <property name="alignment">
          <set>Qt::AlignCenter</set>
         </property>
         <property name="font">
          <font>
           <pointsize>16</pointsize>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="db_button">
         <property name="text">
          <string>材料库</string>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>50</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QPushButton {
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #45a049;
}
QPushButton:pressed {
    background-color: #3d8b40;
}</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="favorites_button">
         <property name="text">
          <string>喜好</string>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>50</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QPushButton {
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #45a049;
}
QPushButton:pressed {
    background-color: #3d8b40;
}</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="add_material_button">
         <property name="text">
          <string>添加材料</string>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>50</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QPushButton {
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #45a049;
}
QPushButton:pressed {
    background-color: #3d8b40;
}</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="verticalSpacer">
         <property name="orientation">
          <enum>Qt::Vertical</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>20</width>
           <height>40</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="center_frame">
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <layout class="QVBoxLayout" name="center_layout">
       <property name="leftMargin">
        <number>15</number>
       </property>
       <property name="topMargin">
        <number>15</number>
       </property>
       <property name="rightMargin">
        <number>15</number>
       </property>
       <property name="bottomMargin">
        <number>15</number>
       </property>
       <item>
        <widget class="QLabel" name="column_title">
         <property name="text">
          <string>材料列表</string>
         </property>
         <property name="font">
          <font>
           <pointsize>14</pointsize>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QScrollArea" name="scrollArea">
         <property name="widgetResizable">
          <bool>true</bool>
         </property>
         <property name="frameShape">
          <enum>QFrame::NoFrame</enum>
         </property>
         <widget class="QWidget" name="scrollAreaWidgetContents">
          <layout class="QVBoxLayout" name="scroll_layout">
           <property name="spacing">
            <number>0</number>
           </property>
           <property name="leftMargin">
            <number>0</number>
           </property>
           <property name="topMargin">
            <number>0</number>
           </property>
           <property name="rightMargin">
            <number>0</number>
           </property>
           <property name="bottomMargin">
            <number>0</number>
           </property>
           <item>
            <widget class="QListWidget" name="materials_list">
             <property name="styleSheet">
              <string notr="true">QListWidget {
    background-color: #f8f8f8;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
}
QListWidget::item {
    padding: 12px;
    border-bottom: 1px solid #eee;
}
QListWidget::item:selected {
    background-color: #e0f7fa;
    color: #006064;
    border-radius: 6px;
}</string>
             </property>
            </widget>
           </item>
          </layout>
         </widget>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="right_frame">
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <layout class="QVBoxLayout" name="right_layout">
       <property name="spacing">
        <number>20</number>
       </property>
       <property name="leftMargin">
        <number>15</number>
       </property>
       <property name="topMargin">
        <number>15</number>
       </property>
       <property name="rightMargin">
        <number>15</number>
       </property>
       <property name="bottomMargin">
        <number>15</number>
       </property>
       <item>
        <widget class="QLabel" name="title_label_2">
         <property name="text">
          <string>材料属性</string>
         </property>
         <property name="font">
          <font>
           <pointsize>14</pointsize>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QVBoxLayout" name="form_layout" stretch="1">
         <property name="spacing">
          <number>15</number>
         </property>
         <item>
          <layout class="QHBoxLayout" name="density_layout">
           <item>
            <widget class="QLabel" name="density_label">
             <property name="text">
              <string>密度 (kg/m³):</string>
             </property>
             <property name="minimumSize">
              <size>
               <width>120</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>120</width>
               <height>16777215</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="density_input">
             <property name="placeholderText">
              <string>输入密度值</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="modulus_layout">
           <item>
            <widget class="QLabel" name="modulus_label">
             <property name="text">
              <string>杨氏模量 (GPa):</string>
             </property>
             <property name="minimumSize">
              <size>
               <width>120</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>120</width>
               <height>16777215</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="modulus_input">
             <property name="placeholderText">
              <string>输入杨氏模量值</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="poisson_layout">
           <item>
            <widget class="QLabel" name="poisson_label">
             <property name="text">
              <string>泊松比:</string>
             </property>
             <property name="minimumSize">
              <size>
               <width>120</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>120</width>
               <height>16777215</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="poisson_input">
             <property name="placeholderText">
              <string>输入泊松比值</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="verticalSpacer_2">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="button_layout">
         <item>
          <widget class="QPushButton" name="modify_button">
           <property name="text">
            <string>修改材料属性</string>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>50</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton {
    background-color: #2196F3;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #0b7dda;
}
QPushButton:pressed {
    background-color: #0a6ebd;
}</string>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QPushButton" name="confirm_modify_button">
           <property name="text">
            <string>确认修改</string>
           </property>
           <property name="minimumSize">
            <size>
             <width>0</width>
             <height>50</height>
            </size>
           </property>
           <property name="styleSheet">
            <string notr="true">QPushButton {
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #45a049;
}
QPushButton:pressed {
    background-color: #3d8b40;
}</string>
           </property>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QFrame" name="add_material_view">
      <property name="frameShape">
       <enum>QFrame::StyledPanel</enum>
      </property>
      <layout class="QVBoxLayout" name="add_layout">
       <property name="spacing">
        <number>20</number>
       </property>
       <property name="leftMargin">
        <number>15</number>
       </property>
       <property name="topMargin">
        <number>15</number>
       </property>
       <property name="rightMargin">
        <number>15</number>
       </property>
       <property name="bottomMargin">
        <number>15</number>
       </property>
       <item>
        <widget class="QLabel" name="title_label_3">
         <property name="text">
          <string>添加新材料</string>
         </property>
         <property name="font">
          <font>
           <pointsize>14</pointsize>
           <weight>75</weight>
           <bold>true</bold>
          </font>
         </property>
        </widget>
       </item>
       <item>
        <layout class="QVBoxLayout" name="form_layout_2" stretch="1">
         <property name="spacing">
          <number>15</number>
         </property>
         <item>
          <layout class="QHBoxLayout" name="name_layout">
           <item>
            <widget class="QLabel" name="name_label">
             <property name="text">
              <string>材料名:</string>
             </property>
             <property name="minimumSize">
              <size>
               <width>120</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>120</width>
               <height>16777215</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="new_name_input">
             <property name="placeholderText">
              <string>输入材料名称</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="density_layout_2">
           <item>
            <widget class="QLabel" name="density_label_2">
             <property name="text">
              <string>密度 (kg/m³):</string>
             </property>
             <property name="minimumSize">
              <size>
               <width>120</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>120</width>
               <height>16777215</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="new_density_input">
             <property name="placeholderText">
              <string>输入密度值</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="modulus_layout_2">
           <item>
            <widget class="QLabel" name="modulus_label_2">
             <property name="text">
              <string>杨氏模量 (GPa):</string>
             </property>
             <property name="minimumSize">
              <size>
               <width>120</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>120</width>
               <height>16777215</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="new_modulus_input">
             <property name="placeholderText">
              <string>输入杨氏模量值</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <layout class="QHBoxLayout" name="poisson_layout_2">
           <item>
            <widget class="QLabel" name="poisson_label_2">
             <property name="text">
              <string>泊松比:</string>
             </property>
             <property name="minimumSize">
              <size>
               <width>120</width>
               <height>0</height>
              </size>
             </property>
             <property name="maximumSize">
              <size>
               <width>120</width>
               <height>16777215</height>
              </size>
             </property>
            </widget>
           </item>
           <item>
            <widget class="QLineEdit" name="new_poisson_input">
             <property name="placeholderText">
              <string>输入泊松比值</string>
             </property>
            </widget>
           </item>
          </layout>
         </item>
         <item>
          <spacer name="verticalSpacer_3">
           <property name="orientation">
            <enum>Qt::Vertical</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>20</width>
             <height>40</height>
            </size>
           </property>
          </spacer>
         </item>
        </layout>
       </item>
       <item>
        <widget class="QPushButton" name="confirm_add_button">
         <property name="text">
          <string>确认添加</string>
         </property>
         <property name="minimumSize">
          <size>
           <width>0</width>
           <height>50</height>
          </size>
         </property>
         <property name="styleSheet">
          <string notr="true">QPushButton {
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
}
QPushButton:hover {
    background-color: #45a049;
}
QPushButton:pressed {
    background-color: #3d8b40;
}</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
 </widget>
 <resources/>
 <connections/>
</ui>