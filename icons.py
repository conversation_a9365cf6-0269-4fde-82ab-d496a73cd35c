"""
材料库界面图标系统
提供Unicode图标和自定义图标支持
"""

from PySide6.QtWidgets import QLabel, QPushButton
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QFont, QPainter, QPen, QBrush, QColor, QPixmap
import math


class UnicodeIcons:
    """Unicode图标常量"""
    # 基础功能图标
    DATABASE = "🗃️"          # 数据库/材料库
    HEART = "❤️"             # 喜好/收藏
    HEART_OUTLINE = "🤍"     # 空心爱心
    PLUS = "➕"              # 添加
    EDIT = "✏️"              # 编辑
    CHECK = "✅"             # 确认
    CROSS = "❌"             # 取消/删除
    SEARCH = "🔍"            # 搜索
    FILTER = "🔽"            # 筛选
    
    # 材料相关图标
    MATERIAL = "🧱"          # 材料
    METAL = "⚙️"             # 金属
    CHEMISTRY = "⚗️"         # 化学/材料属性
    
    # 界面控制图标
    MENU = "☰"               # 菜单
    SETTINGS = "⚙️"          # 设置
    INFO = "ℹ️"              # 信息
    WARNING = "⚠️"           # 警告
    
    # 箭头图标
    ARROW_UP = "⬆️"          # 向上箭头
    ARROW_DOWN = "⬇️"        # 向下箭头
    ARROW_LEFT = "⬅️"        # 向左箭头
    ARROW_RIGHT = "➡️"       # 向右箭头


class IconButton(QPushButton):
    """带图标的按钮组件"""
    
    def __init__(self, icon_text="", text="", parent=None):
        super().__init__(parent)
        self.icon_text = icon_text
        self.button_text = text
        self.setFixedHeight(44)
        self.update_display()
    
    def update_display(self):
        """更新按钮显示"""
        if self.icon_text and self.button_text:
            self.setText(f"{self.icon_text} {self.button_text}")
        elif self.icon_text:
            self.setText(self.icon_text)
        elif self.button_text:
            self.setText(self.button_text)
    
    def set_icon(self, icon_text):
        """设置图标"""
        self.icon_text = icon_text
        self.update_display()
    
    def set_text(self, text):
        """设置文字"""
        self.button_text = text
        self.update_display()


class IconLabel(QLabel):
    """带图标的标签组件"""
    
    def __init__(self, icon_text="", text="", parent=None):
        super().__init__(parent)
        self.icon_text = icon_text
        self.label_text = text
        self.update_display()
    
    def update_display(self):
        """更新标签显示"""
        if self.icon_text and self.label_text:
            self.setText(f"{self.icon_text} {self.label_text}")
        elif self.icon_text:
            self.setText(self.icon_text)
        elif self.label_text:
            self.setText(self.label_text)
    
    def set_icon(self, icon_text):
        """设置图标"""
        self.icon_text = icon_text
        self.update_display()
    
    def set_text(self, text):
        """设置文字"""
        self.label_text = text
        self.update_display()


class CustomStarButton(QPushButton):
    """自定义五角星按钮（改进版）"""
    
    def __init__(self, is_favorite=False, size=24, parent=None):
        super().__init__(parent)
        self.setFixedSize(size, size)
        self.setFlat(True)
        self.is_favorite = is_favorite
        self.star_size = size
        self.clicked.connect(self.toggle_favorite)
        
        # 设置工具提示
        self.update_tooltip()
    
    def toggle_favorite(self):
        """切换收藏状态"""
        self.is_favorite = not self.is_favorite
        self.update_tooltip()
        self.update()
        
        # 触发状态改变回调
        if hasattr(self, 'on_toggle'):
            self.on_toggle(self.is_favorite)
    
    def update_tooltip(self):
        """更新工具提示"""
        if self.is_favorite:
            self.setToolTip("取消收藏")
        else:
            self.setToolTip("添加到收藏")
    
    def paintEvent(self, event):
        """绘制五角星"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 计算五角星参数
        size = min(self.width(), self.height()) - 4
        center_x = self.width() / 2
        center_y = self.height() / 2
        outer_radius = size / 2
        inner_radius = size / 4
        
        # 设置颜色
        if self.is_favorite:
            painter.setBrush(QBrush(QColor("#FFD700")))  # 金色填充
            painter.setPen(QPen(QColor("#DAA520"), 2))   # 金色边框
        else:
            painter.setBrush(Qt.NoBrush)
            painter.setPen(QPen(QColor("#BDBDBD"), 2))   # 灰色边框
        
        # 计算五角星顶点
        points = []
        for i in range(10):
            angle = math.pi / 2 + i * math.pi / 5
            if i % 2 == 0:  # 外点
                x = center_x + outer_radius * math.cos(angle)
                y = center_y - outer_radius * math.sin(angle)
            else:  # 内点
                x = center_x + inner_radius * math.cos(angle)
                y = center_y - inner_radius * math.sin(angle)
            points.append((x, y))
        
        # 绘制五角星
        from PySide6.QtCore import QPointF
        star_points = [QPointF(x, y) for x, y in points]
        painter.drawPolygon(star_points)


class MaterialIcon:
    """材料类型图标映射"""
    
    ICON_MAP = {
        "钢材": "🔩",
        "铝合金": "✈️",
        "铜": "🔶",
        "钛合金": "🚀",
        "镁合金": "⚡",
        "镍合金": "🔧",
        "陶瓷": "🏺",
        "玻璃": "💎",
        "橡胶": "⚫",
        "木材": "🌳",
        "塑料": "🧪",
        "复合材料": "🔬",
        "默认": "🧱"
    }
    
    @staticmethod
    def get_material_icon(material_name):
        """根据材料名称获取对应图标"""
        return MaterialIcon.ICON_MAP.get(material_name, MaterialIcon.ICON_MAP["默认"])


class IconFactory:
    """图标工厂类"""
    
    @staticmethod
    def create_icon_button(icon_type, text="", size="medium", parent=None):
        """创建图标按钮"""
        icon_map = {
            "database": UnicodeIcons.DATABASE,
            "heart": UnicodeIcons.HEART,
            "heart_outline": UnicodeIcons.HEART_OUTLINE,
            "plus": UnicodeIcons.PLUS,
            "edit": UnicodeIcons.EDIT,
            "check": UnicodeIcons.CHECK,
            "cross": UnicodeIcons.CROSS,
            "search": UnicodeIcons.SEARCH,
            "filter": UnicodeIcons.FILTER,
        }
        
        icon_text = icon_map.get(icon_type, "")
        button = IconButton(icon_text, text, parent)
        
        # 设置字体大小
        font_size = {"small": 12, "medium": 14, "large": 16}[size]
        font = QFont("Microsoft YaHei", font_size)
        button.setFont(font)
        
        return button
    
    @staticmethod
    def create_icon_label(icon_type, text="", size="medium", parent=None):
        """创建图标标签"""
        icon_map = {
            "database": UnicodeIcons.DATABASE,
            "material": UnicodeIcons.MATERIAL,
            "chemistry": UnicodeIcons.CHEMISTRY,
            "info": UnicodeIcons.INFO,
            "warning": UnicodeIcons.WARNING,
        }
        
        icon_text = icon_map.get(icon_type, "")
        label = IconLabel(icon_text, text, parent)
        
        # 设置字体大小
        font_size = {"small": 12, "medium": 14, "large": 16}[size]
        font = QFont("Microsoft YaHei", font_size)
        label.setFont(font)
        
        return label
    
    @staticmethod
    def create_star_button(is_favorite=False, size=24, parent=None):
        """创建五角星按钮"""
        return CustomStarButton(is_favorite, size, parent)


# 预定义的图标组合
class PresetIcons:
    """预设图标组合"""
    
    @staticmethod
    def material_database_button(parent=None):
        """材料库按钮"""
        return IconFactory.create_icon_button("database", "材料库", parent=parent)
    
    @staticmethod
    def favorites_button(parent=None):
        """收藏按钮"""
        return IconFactory.create_icon_button("heart", "喜好", parent=parent)
    
    @staticmethod
    def add_material_button(parent=None):
        """添加材料按钮"""
        return IconFactory.create_icon_button("plus", "添加材料", parent=parent)
    
    @staticmethod
    def edit_button(parent=None):
        """编辑按钮"""
        return IconFactory.create_icon_button("edit", "修改", parent=parent)
    
    @staticmethod
    def confirm_button(parent=None):
        """确认按钮"""
        return IconFactory.create_icon_button("check", "确认", parent=parent)
    
    @staticmethod
    def search_button(parent=None):
        """搜索按钮"""
        return IconFactory.create_icon_button("search", "搜索", parent=parent)
