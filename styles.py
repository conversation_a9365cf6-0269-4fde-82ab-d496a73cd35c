"""
材料库界面统一样式系统
提供配色方案、字体、间距等UI设计常量和样式字符串
"""

from PySide6.QtCore import Qt
from PySide6.QtGui import QFont


class Colors:
    """配色方案"""
    # 主色调 - 绿色系
    PRIMARY_DARK = "#2E7D32"      # 深绿色
    PRIMARY = "#4CAF50"           # 主绿色
    PRIMARY_LIGHT = "#81C784"     # 浅绿色
    PRIMARY_LIGHTER = "#C8E6C9"   # 更浅绿色
    
    # 辅助色 - 蓝色系
    SECONDARY = "#1976D2"         # 蓝色
    SECONDARY_LIGHT = "#42A5F5"   # 浅蓝色
    
    # 中性色
    BACKGROUND = "#FAFAFA"        # 主背景
    SURFACE = "#FFFFFF"           # 卡片背景
    SURFACE_VARIANT = "#F5F5F5"   # 变体背景
    
    # 边框和分割线
    BORDER = "#E0E0E0"           # 边框色
    BORDER_LIGHT = "#F0F0F0"     # 浅边框色
    DIVIDER = "#EEEEEE"          # 分割线
    
    # 文字颜色
    TEXT_PRIMARY = "#212121"      # 主要文字
    TEXT_SECONDARY = "#757575"    # 次要文字
    TEXT_DISABLED = "#BDBDBD"     # 禁用文字
    TEXT_ON_PRIMARY = "#FFFFFF"   # 主色上的文字
    
    # 状态色
    SUCCESS = "#4CAF50"          # 成功
    WARNING = "#FF9800"          # 警告
    ERROR = "#F44336"            # 错误
    INFO = "#2196F3"             # 信息
    
    # 特殊色
    FAVORITE_GOLD = "#FFD700"    # 收藏金色
    FAVORITE_BORDER = "#DAA520"  # 收藏边框
    
    # 悬停和选中状态
    HOVER_OVERLAY = "rgba(0, 0, 0, 0.04)"
    SELECTED_OVERLAY = "rgba(76, 175, 80, 0.12)"
    PRESSED_OVERLAY = "rgba(0, 0, 0, 0.08)"


class Fonts:
    """字体系统"""
    # 字体族
    FAMILY_PRIMARY = "Microsoft YaHei, Arial, sans-serif"
    FAMILY_MONOSPACE = "Consolas, Monaco, monospace"
    
    # 字体大小
    SIZE_LARGE = 16      # 大标题
    SIZE_MEDIUM = 14     # 中等文字
    SIZE_SMALL = 12      # 小文字
    SIZE_CAPTION = 10    # 说明文字
    
    @staticmethod
    def get_font(size=14, bold=False, family=None):
        """获取字体对象"""
        font = QFont(family or Fonts.FAMILY_PRIMARY, size)
        if bold:
            font.setBold(True)
        return font


class Spacing:
    """间距系统"""
    XS = 4      # 极小间距
    SM = 8      # 小间距
    MD = 16     # 中等间距
    LG = 24     # 大间距
    XL = 32     # 极大间距
    
    # 特定用途间距
    PADDING_SMALL = 8
    PADDING_MEDIUM = 16
    PADDING_LARGE = 24
    
    MARGIN_SMALL = 8
    MARGIN_MEDIUM = 16
    MARGIN_LARGE = 24


class BorderRadius:
    """圆角系统"""
    SMALL = 4
    MEDIUM = 8
    LARGE = 12
    ROUND = 50  # 圆形


class Shadows:
    """阴影系统"""
    LIGHT = "0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24)"
    MEDIUM = "0 3px 6px rgba(0, 0, 0, 0.16), 0 3px 6px rgba(0, 0, 0, 0.23)"
    HEAVY = "0 10px 20px rgba(0, 0, 0, 0.19), 0 6px 6px rgba(0, 0, 0, 0.23)"


class StyleSheets:
    """样式字符串集合"""
    
    @staticmethod
    def primary_button(size="medium"):
        """主要按钮样式"""
        height = {"small": 36, "medium": 44, "large": 52}[size]
        return f"""
            QPushButton {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {Colors.PRIMARY}, stop:1 {Colors.PRIMARY_DARK});
                color: {Colors.TEXT_ON_PRIMARY};
                border: none;
                border-radius: {BorderRadius.MEDIUM}px;
                font-family: {Fonts.FAMILY_PRIMARY};
                font-size: {Fonts.SIZE_MEDIUM}px;
                font-weight: bold;
                padding: 0 {Spacing.MD}px;
                min-height: {height}px;
            }}
            QPushButton:hover {{
                background: qlineargradient(x1:0, y1:0, x2:0, y2:1,
                    stop:0 {Colors.PRIMARY_LIGHT}, stop:1 {Colors.PRIMARY});
            }}
            QPushButton:pressed {{
                background: {Colors.PRIMARY_DARK};
            }}
            QPushButton:disabled {{
                background: {Colors.TEXT_DISABLED};
                color: {Colors.SURFACE};
            }}
        """
    
    @staticmethod
    def secondary_button(size="medium"):
        """次要按钮样式"""
        height = {"small": 36, "medium": 44, "large": 52}[size]
        return f"""
            QPushButton {{
                background: {Colors.SURFACE};
                color: {Colors.PRIMARY};
                border: 2px solid {Colors.PRIMARY};
                border-radius: {BorderRadius.MEDIUM}px;
                font-family: {Fonts.FAMILY_PRIMARY};
                font-size: {Fonts.SIZE_MEDIUM}px;
                font-weight: bold;
                padding: 0 {Spacing.MD}px;
                min-height: {height}px;
            }}
            QPushButton:hover {{
                background: {Colors.PRIMARY_LIGHTER};
                border-color: {Colors.PRIMARY_DARK};
            }}
            QPushButton:pressed {{
                background: {Colors.PRIMARY_LIGHT};
            }}
            QPushButton:disabled {{
                background: {Colors.SURFACE_VARIANT};
                color: {Colors.TEXT_DISABLED};
                border-color: {Colors.TEXT_DISABLED};
            }}
        """
    
    @staticmethod
    def input_field():
        """输入框样式"""
        return f"""
            QLineEdit {{
                background: {Colors.SURFACE};
                border: 2px solid {Colors.BORDER};
                border-radius: {BorderRadius.MEDIUM}px;
                padding: {Spacing.SM}px {Spacing.MD}px;
                font-family: {Fonts.FAMILY_PRIMARY};
                font-size: {Fonts.SIZE_MEDIUM}px;
                color: {Colors.TEXT_PRIMARY};
                selection-background-color: {Colors.PRIMARY_LIGHT};
            }}
            QLineEdit:focus {{
                border-color: {Colors.PRIMARY};
                background: {Colors.SURFACE};
            }}
            QLineEdit:hover {{
                border-color: {Colors.PRIMARY_LIGHT};
            }}
            QLineEdit:disabled {{
                background: {Colors.SURFACE_VARIANT};
                color: {Colors.TEXT_DISABLED};
                border-color: {Colors.BORDER_LIGHT};
            }}
            QLineEdit::placeholder {{
                color: {Colors.TEXT_SECONDARY};
            }}
        """
    
    @staticmethod
    def card_frame():
        """卡片框架样式"""
        return f"""
            QFrame {{
                background: {Colors.SURFACE};
                border: 1px solid {Colors.BORDER_LIGHT};
                border-radius: {BorderRadius.LARGE}px;
            }}
        """
    
    @staticmethod
    def list_widget():
        """列表控件样式"""
        return f"""
            QListWidget {{
                background: {Colors.SURFACE};
                border: 1px solid {Colors.BORDER};
                border-radius: {BorderRadius.MEDIUM}px;
                font-family: {Fonts.FAMILY_PRIMARY};
                font-size: {Fonts.SIZE_MEDIUM}px;
                outline: none;
            }}
            QListWidget::item {{
                padding: {Spacing.MD}px;
                border-bottom: 1px solid {Colors.BORDER_LIGHT};
                border-radius: {BorderRadius.SMALL}px;
                margin: 2px;
            }}
            QListWidget::item:hover {{
                background: {Colors.SURFACE_VARIANT};
            }}
            QListWidget::item:selected {{
                background: {Colors.SELECTED_OVERLAY};
                color: {Colors.TEXT_PRIMARY};
                border: 2px solid {Colors.PRIMARY_LIGHT};
            }}
        """
    
    @staticmethod
    def title_label(size="medium"):
        """标题标签样式"""
        font_size = {"small": Fonts.SIZE_MEDIUM, "medium": Fonts.SIZE_LARGE, "large": 20}[size]
        return f"""
            QLabel {{
                color: {Colors.TEXT_PRIMARY};
                font-family: {Fonts.FAMILY_PRIMARY};
                font-size: {font_size}px;
                font-weight: bold;
                padding: {Spacing.SM}px 0;
            }}
        """
    
    @staticmethod
    def info_label():
        """信息标签样式"""
        return f"""
            QLabel {{
                color: {Colors.TEXT_SECONDARY};
                font-family: {Fonts.FAMILY_PRIMARY};
                font-size: {Fonts.SIZE_SMALL}px;
                padding: {Spacing.XS}px 0;
            }}
        """


class Animations:
    """动画参数"""
    DURATION_FAST = 150      # 快速动画
    DURATION_NORMAL = 300    # 正常动画
    DURATION_SLOW = 500      # 慢速动画
    
    EASING_STANDARD = "ease-out"
    EASING_SMOOTH = "ease-in-out"
