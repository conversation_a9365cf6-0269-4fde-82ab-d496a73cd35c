"""
材料库界面动画效果系统
提供平滑的过渡动画和交互反馈
"""

from PySide6.QtCore import QPropertyAnimation, QEasingCurve, QRect, QTimer, QObject, Signal
from PySide6.QtWidgets import QWidget, QGraphicsOpacityEffect
from PySide6.QtGui import QColor
from styles import Colors


class AnimationManager(QObject):
    """动画管理器"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.animations = []
    
    def add_animation(self, animation):
        """添加动画到管理器"""
        self.animations.append(animation)
        animation.finished.connect(lambda: self.remove_animation(animation))
    
    def remove_animation(self, animation):
        """从管理器中移除动画"""
        if animation in self.animations:
            self.animations.remove(animation)
    
    def stop_all_animations(self):
        """停止所有动画"""
        for animation in self.animations[:]:
            animation.stop()
        self.animations.clear()


class FadeAnimation:
    """淡入淡出动画"""
    
    @staticmethod
    def fade_in(widget, duration=300, start_opacity=0.0, end_opacity=1.0):
        """淡入动画"""
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        animation = QPropertyAnimation(effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(start_opacity)
        animation.setEndValue(end_opacity)
        animation.setEasingCurve(QEasingCurve.OutCubic)
        
        animation.start()
        return animation
    
    @staticmethod
    def fade_out(widget, duration=300, start_opacity=1.0, end_opacity=0.0):
        """淡出动画"""
        effect = QGraphicsOpacityEffect()
        widget.setGraphicsEffect(effect)
        
        animation = QPropertyAnimation(effect, b"opacity")
        animation.setDuration(duration)
        animation.setStartValue(start_opacity)
        animation.setEndValue(end_opacity)
        animation.setEasingCurve(QEasingCurve.OutCubic)
        
        animation.start()
        return animation


class SlideAnimation:
    """滑动动画"""
    
    @staticmethod
    def slide_in_from_right(widget, duration=400):
        """从右侧滑入"""
        parent = widget.parent()
        if not parent:
            return None
        
        # 获取目标位置
        target_geometry = widget.geometry()
        
        # 设置起始位置（在右侧外面）
        start_geometry = QRect(
            parent.width(),
            target_geometry.y(),
            target_geometry.width(),
            target_geometry.height()
        )
        widget.setGeometry(start_geometry)
        widget.show()
        
        # 创建动画
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        animation.setStartValue(start_geometry)
        animation.setEndValue(target_geometry)
        animation.setEasingCurve(QEasingCurve.OutCubic)
        
        animation.start()
        return animation
    
    @staticmethod
    def slide_out_to_right(widget, duration=400):
        """向右侧滑出"""
        parent = widget.parent()
        if not parent:
            return None
        
        # 获取当前位置
        current_geometry = widget.geometry()
        
        # 设置目标位置（在右侧外面）
        target_geometry = QRect(
            parent.width(),
            current_geometry.y(),
            current_geometry.width(),
            current_geometry.height()
        )
        
        # 创建动画
        animation = QPropertyAnimation(widget, b"geometry")
        animation.setDuration(duration)
        animation.setStartValue(current_geometry)
        animation.setEndValue(target_geometry)
        animation.setEasingCurve(QEasingCurve.InCubic)
        
        # 动画完成后隐藏控件
        animation.finished.connect(widget.hide)
        
        animation.start()
        return animation


class ButtonAnimation:
    """按钮动画效果"""
    
    @staticmethod
    def press_effect(button, duration=150):
        """按钮按下效果"""
        original_geometry = button.geometry()
        
        # 轻微缩小
        pressed_geometry = QRect(
            original_geometry.x() + 2,
            original_geometry.y() + 2,
            original_geometry.width() - 4,
            original_geometry.height() - 4
        )
        
        # 按下动画
        press_animation = QPropertyAnimation(button, b"geometry")
        press_animation.setDuration(duration // 2)
        press_animation.setStartValue(original_geometry)
        press_animation.setEndValue(pressed_geometry)
        press_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 恢复动画
        release_animation = QPropertyAnimation(button, b"geometry")
        release_animation.setDuration(duration // 2)
        release_animation.setStartValue(pressed_geometry)
        release_animation.setEndValue(original_geometry)
        release_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 连接动画
        press_animation.finished.connect(release_animation.start)
        press_animation.start()
        
        return press_animation, release_animation


class ViewSwitchAnimation:
    """视图切换动画"""
    
    @staticmethod
    def switch_views(old_view, new_view, duration=350):
        """切换视图动画"""
        if not old_view or not new_view:
            return None, None
        
        # 淡出旧视图
        fade_out_animation = FadeAnimation.fade_out(old_view, duration // 2)
        
        # 淡入新视图
        def show_new_view():
            old_view.hide()
            new_view.show()
            FadeAnimation.fade_in(new_view, duration // 2)
        
        # 连接动画
        fade_out_animation.finished.connect(show_new_view)
        
        return fade_out_animation


class LoadingAnimation:
    """加载动画"""
    
    def __init__(self, widget, parent=None):
        self.widget = widget
        self.timer = QTimer(parent)
        self.timer.timeout.connect(self.update_loading)
        self.loading_state = 0
        self.is_loading = False
        
        # 保存原始样式
        self.original_style = widget.styleSheet()
    
    def start_loading(self, interval=500):
        """开始加载动画"""
        if self.is_loading:
            return
        
        self.is_loading = True
        self.loading_state = 0
        self.timer.start(interval)
    
    def stop_loading(self):
        """停止加载动画"""
        if not self.is_loading:
            return
        
        self.is_loading = False
        self.timer.stop()
        self.widget.setStyleSheet(self.original_style)
    
    def update_loading(self):
        """更新加载状态"""
        self.loading_state = (self.loading_state + 1) % 4
        
        # 创建脉冲效果
        opacity = 0.3 + (self.loading_state * 0.2)
        loading_style = f"""
            {self.original_style}
            background-color: rgba(76, 175, 80, {opacity});
        """
        self.widget.setStyleSheet(loading_style)


class HoverEffect:
    """悬停效果"""
    
    @staticmethod
    def add_hover_effect(widget, hover_color=None, normal_color=None):
        """为控件添加悬停效果"""
        if hover_color is None:
            hover_color = Colors.SURFACE_VARIANT
        if normal_color is None:
            normal_color = Colors.SURFACE
        
        original_style = widget.styleSheet()
        
        def enter_event(event):
            widget.setStyleSheet(f"""
                {original_style}
                background-color: {hover_color};
            """)
        
        def leave_event(event):
            widget.setStyleSheet(original_style)
        
        # 保存原始事件处理器
        original_enter = widget.enterEvent
        original_leave = widget.leaveEvent
        
        # 设置新的事件处理器
        widget.enterEvent = enter_event
        widget.leaveEvent = leave_event
        
        return original_enter, original_leave


class PulseAnimation:
    """脉冲动画"""
    
    @staticmethod
    def create_pulse(widget, duration=1000, scale_factor=1.05):
        """创建脉冲动画"""
        original_geometry = widget.geometry()
        
        # 计算放大后的几何形状
        width_diff = int(original_geometry.width() * (scale_factor - 1))
        height_diff = int(original_geometry.height() * (scale_factor - 1))
        
        scaled_geometry = QRect(
            original_geometry.x() - width_diff // 2,
            original_geometry.y() - height_diff // 2,
            original_geometry.width() + width_diff,
            original_geometry.height() + height_diff
        )
        
        # 放大动画
        expand_animation = QPropertyAnimation(widget, b"geometry")
        expand_animation.setDuration(duration // 2)
        expand_animation.setStartValue(original_geometry)
        expand_animation.setEndValue(scaled_geometry)
        expand_animation.setEasingCurve(QEasingCurve.OutCubic)
        
        # 缩小动画
        shrink_animation = QPropertyAnimation(widget, b"geometry")
        shrink_animation.setDuration(duration // 2)
        shrink_animation.setStartValue(scaled_geometry)
        shrink_animation.setEndValue(original_geometry)
        shrink_animation.setEasingCurve(QEasingCurve.InCubic)
        
        # 连接动画
        expand_animation.finished.connect(shrink_animation.start)
        expand_animation.start()
        
        return expand_animation, shrink_animation


# 全局动画管理器实例
animation_manager = AnimationManager()
