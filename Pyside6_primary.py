# 以下为 b站视频白月黑羽Pyside6相关代码  代码网址： https://www.byhy.net/py/qt/qt_01/

# from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton, QPlainTextEdit, QMessageBox # widget 控件
#
# def handleCalc():
#     info = textEdit.toPlainText() # 获取文本信息
#
#     # 薪资20000 以上 和 以下 的人员名单
#     salary_above_20k = ''
#     salary_below_20k = ''
#     for line in info.splitlines():  # 将字符串按行分割
#         if not line.strip(): # line.strip() 去除每一行头尾的空格，如果去除完毕后是空的，那必须排除，如何判断是不是空的？
#             # True:跳过该此循环，False:继续执行   空即F, not空即T，即如果是空的，直接跳过该次循环
#             continue  # continue并非是继续执行，而是跳过当前循环进行下一次循环
#         parts = line.split(' ') # 对line用空格进行分割
#         # 去掉列表中的空字符串内容
#         parts = [p for p in parts if p] # result = [expression for item in iterable if condition]
#         # 即对parts进行遍历得到p, if p 则 p  意思是如果遍历得到的p是T，即非空，则p，则保留p
#         name, salary, age = parts # 变量1, 变量2, ..., 变量n = [元素1, 元素2, ..., 元素n] 意思就是将parts的三个部分分别幅值给n,s,a
#         if int(salary) >= 20000:
#                 salary_above_20k += name + '\n'
#         else:
#             salary_below_20k += name + '\n'
#
#     QMessageBox.about(window,
#                     '统计结果',
#                     f'''薪资20000 以上的有：\n{salary_above_20k}
#                     \n薪资20000 以下的有：\n{salary_below_20k}'''
#                           )
#
# app = QApplication()  # QApplication 提供了整个图形界面程序的底层管理功能
#
# window = QMainWindow() # 主窗口
# window.resize(500, 400) # 主窗口界面的像素尺寸
# window.move(300, 310) # 主窗口界面相对左上角屏幕的位置
# window.setWindowTitle('薪资统计')
#
# textEdit = QPlainTextEdit(window) # 文本编辑框 是在主窗口下面
# textEdit.setPlaceholderText("请输入薪资表") # placeholder 提示符文本
# textEdit.move(10,25) # 相对于父窗口的左上角
# textEdit.resize(300,350)
#
# button = QPushButton('统计', window) # 按钮
# button.move(380,80)
# button.clicked.connect(handleCalc) # 当点击按钮时，连接到handleCalc,执行handleCalc这一命令
#
# window.show() # 必要
# app.exec() # 运行 execute

# 这样就可以运行，但是 上面的代码把控件对应的变量名全部作为全局变量。如果要设计稍微复杂一些的程序，就会出现太多的控件对应的变量名。
# 其实我也不太懂，反正似乎 把一个窗口和其包含的控件，对应的代码 全部封装到类中，这样会好一点，即下

# from PySide6.QtWidgets import QApplication, QMainWindow, QPushButton,  QPlainTextEdit,QMessageBox
#
# class Stats:
#     def __init__(self):
#         self.window = QMainWindow()
#         self.window.resize(500, 400)
#         self.window.move(300, 300)
#         self.window.setWindowTitle('薪资统计')
#
#         self.textEdit = QPlainTextEdit(self.window)
#         self.textEdit.setPlaceholderText("请输入薪资表")
#         self.textEdit.move(10, 25)
#         self.textEdit.resize(300, 350)
#
#         self.button = QPushButton('统计', self.window)
#         self.button.move(380, 80)
#
#         self.button.clicked.connect(self.handleCalc)
#
#
#     def handleCalc(self):
#         info = self.textEdit.toPlainText()
#
#         # 薪资20000 以上 和 以下 的人员名单
#         salary_above_20k = ''
#         salary_below_20k = ''
#         for line in info.splitlines():
#             if not line.strip():
#                 continue
#             parts = line.split(' ')
#             # 去掉列表中的空字符串内容
#             parts = [p for p in parts if p]
#             name,salary,age = parts
#             if int(salary) >= 20000:
#                 salary_above_20k += name + '\n'
#             else:
#                 salary_below_20k += name + '\n'
#
#         QMessageBox.about(self.window,
#                     '统计结果',
#                     f'''薪资20000 以上的有：\n{salary_above_20k}
#                     \n薪资20000 以下的有：\n{salary_below_20k}'''
#                     )
#
# app = QApplication()
# stats = Stats()
# stats.window.show()
# app.exec()

# 我们可以通过Designer来设计ui界面，这样就可以避免手动输入程序设计界面了

from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtUiTools import QUiLoader

class Stats:

    def __init__(self):
        # 从文件中加载UI定义
        # 从 UI 定义中动态 创建一个相应的窗口对象
        # 注意：里面的控件对象也成为窗口对象的属性了
        # 比如 self.ui.button , self.ui.textEdit
        self.ui = QUiLoader().load(r'D:\Projects\2025\2025.7\Auto Mechanical\self_Pyside6\ui_windows\bili_byhy_1.ui')
        # 加载ui文件，在path前面加 r 的原因是反斜杠是转义字符，系统默认有特殊含义，比如\n等等，加上r就可以转变为纯字符串了
        self.ui.Button.clicked.connect(self.handleCalc)  # 直接省略了大部分操作，其余几乎一样

    def handleCalc(self):
        info = self.ui.TextEdit.toPlainText()

        salary_above_20k = ''
        salary_below_20k = ''
        for line in info.splitlines():
            if not line.strip():
                continue
            parts = line.split(' ')

            parts = [p for p in parts if p]
            name, salary, age = parts
            if int(salary) >= 20000:
                salary_above_20k += name + '\n'
            else:
                salary_below_20k += name + '\n'

        QMessageBox.about(self.ui,
                          '统计结果',
                          f'''薪资20000 以上的有：\n{salary_above_20k}
                    \n薪资20000 以下的有：\n{salary_below_20k}'''
                          )

app = QApplication()
stats = Stats()
stats.ui.show()
app.exec()
