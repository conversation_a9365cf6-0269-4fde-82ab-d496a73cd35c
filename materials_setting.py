import sys
import math
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QListWidget, QScrollArea, QLabel, QLineEdit,
    QListWidgetItem, QFrame, QSizePolicy, QMessageBox)
from PySide6.QtUiTools import QUiLoader
from PySide6.QtCore import Qt, QSize, QPointF, QFile
from PySide6.QtGui import QFont, QColor, QPainter, QPen, QBrush

# 导入新的样式和图标系统
from styles import StyleSheets, Colors, Fonts, Spacing
from icons import IconFactory, CustomStarButton, MaterialIcon
from animations import FadeAnimation, ViewSwitchAnimation, ButtonAnimation, animation_manager, LoadingAnimation


# 使用新的CustomStarButton替代原来的StarButton
# 原StarButton类已移至icons.py中作为CustomStarButton


class MaterialDatabaseApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("材料数据库管理系统")
        self.setGeometry(100, 100, 1200, 700)

        # 加载UI文件
        self.load_ui()

        # 初始化材料数据
        self.materials = [
            {"name": "钢材", "density": "7850", "youngs_modulus": "200", "poissons_ratio": "0.3", "is_favorite": True},
            {"name": "铝合金", "density": "2700", "youngs_modulus": "70", "poissons_ratio": "0.33",
             "is_favorite": False},
            {"name": "铜", "density": "8960", "youngs_modulus": "110", "poissons_ratio": "0.34", "is_favorite": False},
            {"name": "钛合金", "density": "4500", "youngs_modulus": "116", "poissons_ratio": "0.32",
             "is_favorite": False},
            {"name": "镁合金", "density": "1740", "youngs_modulus": "45", "poissons_ratio": "0.35",
             "is_favorite": False},
            {"name": "镍合金", "density": "8900", "youngs_modulus": "200", "poissons_ratio": "0.31",
             "is_favorite": False},
            {"name": "陶瓷", "density": "3900", "youngs_modulus": "350", "poissons_ratio": "0.22",
             "is_favorite": False},
            {"name": "玻璃", "density": "2500", "youngs_modulus": "70", "poissons_ratio": "0.23", "is_favorite": False},
            {"name": "橡胶", "density": "1100", "youngs_modulus": "0.01", "poissons_ratio": "0.49",
             "is_favorite": False},
            {"name": "木材", "density": "600", "youngs_modulus": "10", "poissons_ratio": "0.3", "is_favorite": False},
        ]

        # 当前显示模式：0-材料库，1-喜好
        self.current_mode = 0
        # 当前编辑状态
        self.editing_mode = False

        # 设置样式
        self.set_styles()

        # 连接信号
        self.connect_signals()

        # 加载材料到列表
        self.load_materials()

        # 默认显示第一个材料
        if self.materials:
            self.materials_list.setCurrentRow(0)
            self.display_material_properties(self.materials[0])

        # 隐藏确认修改按钮
        self.confirm_modify_button.hide()

        # 设置添加材料视图初始状态
        self.add_material_view.hide()

        # 优化布局和响应式设计
        self.optimize_layout()

        # 添加搜索功能
        self.setup_search_functionality()

    def load_ui(self):
        """加载UI文件"""
        ui_file = QFile(r"D:\Projects\2025\2025.7\Auto Mechanical\self_Pyside6\ui_windows\material_database.ui")
        if not ui_file.open(QFile.ReadOnly):
            print(f"Cannot open {ui_file.fileName()}: {ui_file.errorString()}")
            sys.exit(-1)

        loader = QUiLoader()
        self.ui = loader.load(ui_file)
        ui_file.close()

        if not self.ui:
            print(loader.errorString())
            sys.exit(-1)

        self.setCentralWidget(self.ui)

        # 获取UI中的控件
        self.left_frame = self.ui.findChild(QFrame, "left_frame")
        self.db_button = self.ui.findChild(QPushButton, "db_button")
        self.favorites_button = self.ui.findChild(QPushButton, "favorites_button")
        self.add_material_button = self.ui.findChild(QPushButton, "add_material_button")

        self.center_frame = self.ui.findChild(QFrame, "center_frame")
        self.column_title = self.ui.findChild(QLabel, "column_title")
        self.scrollArea = self.ui.findChild(QScrollArea, "scrollArea")
        self.materials_list = self.ui.findChild(QListWidget, "materials_list")

        self.right_frame = self.ui.findChild(QFrame, "right_frame")
        self.density_input = self.ui.findChild(QLineEdit, "density_input")
        self.modulus_input = self.ui.findChild(QLineEdit, "modulus_input")
        self.poisson_input = self.ui.findChild(QLineEdit, "poisson_input")
        self.modify_button = self.ui.findChild(QPushButton, "modify_button")
        self.confirm_modify_button = self.ui.findChild(QPushButton, "confirm_modify_button")

        self.add_material_view = self.ui.findChild(QFrame, "add_material_view")
        self.new_name_input = self.ui.findChild(QLineEdit, "new_name_input")
        self.new_density_input = self.ui.findChild(QLineEdit, "new_density_input")
        self.new_modulus_input = self.ui.findChild(QLineEdit, "new_modulus_input")
        self.new_poisson_input = self.ui.findChild(QLineEdit, "new_poisson_input")
        self.confirm_add_button = self.ui.findChild(QPushButton, "confirm_add_button")

    def set_styles(self):
        """设置控件样式"""
        # 应用新的样式系统

        # 输入框样式
        input_style = StyleSheets.input_field()
        self.density_input.setStyleSheet(input_style)
        self.modulus_input.setStyleSheet(input_style)
        self.poisson_input.setStyleSheet(input_style)
        self.new_name_input.setStyleSheet(input_style)
        self.new_density_input.setStyleSheet(input_style)
        self.new_modulus_input.setStyleSheet(input_style)
        self.new_poisson_input.setStyleSheet(input_style)

        # 主要按钮样式（绿色按钮）
        primary_button_style = StyleSheets.primary_button()
        self.db_button.setStyleSheet(primary_button_style)
        self.favorites_button.setStyleSheet(primary_button_style)
        self.add_material_button.setStyleSheet(primary_button_style)
        self.confirm_modify_button.setStyleSheet(primary_button_style)
        self.confirm_add_button.setStyleSheet(primary_button_style)

        # 次要按钮样式（蓝色按钮）
        secondary_button_style = StyleSheets.secondary_button()
        self.modify_button.setStyleSheet(secondary_button_style)

        # 列表样式
        self.materials_list.setStyleSheet(StyleSheets.list_widget())

        # 标题样式
        title_style = StyleSheets.title_label("large")
        self.ui.findChild(QLabel, "title_label").setStyleSheet(title_style)
        self.ui.findChild(QLabel, "title_label_2").setStyleSheet(title_style)
        self.ui.findChild(QLabel, "title_label_3").setStyleSheet(title_style)

        # 列标题样式
        column_title_style = StyleSheets.title_label("medium")
        self.column_title.setStyleSheet(column_title_style)

        # 框架样式
        frame_style = StyleSheets.card_frame()
        self.left_frame.setStyleSheet(frame_style)
        self.center_frame.setStyleSheet(frame_style)
        self.right_frame.setStyleSheet(frame_style)
        self.add_material_view.setStyleSheet(frame_style)

        # 表单标签样式
        label_style = StyleSheets.info_label()
        # 右侧属性标签
        self.ui.findChild(QLabel, "density_label").setStyleSheet(label_style)
        self.ui.findChild(QLabel, "modulus_label").setStyleSheet(label_style)
        self.ui.findChild(QLabel, "poisson_label").setStyleSheet(label_style)
        # 添加材料表单标签
        self.ui.findChild(QLabel, "name_label").setStyleSheet(label_style)
        self.ui.findChild(QLabel, "density_label_2").setStyleSheet(label_style)
        self.ui.findChild(QLabel, "modulus_label_2").setStyleSheet(label_style)
        self.ui.findChild(QLabel, "poisson_label_2").setStyleSheet(label_style)

    def optimize_layout(self):
        """优化界面布局和响应式设计"""
        # 设置窗口最小尺寸和图标
        self.setMinimumSize(1000, 600)
        self.setWindowTitle("🧱 材料数据库管理系统")

        # 优化左侧面板宽度
        self.left_frame.setFixedWidth(200)
        self.left_frame.setMaximumWidth(250)

        # 优化中间面板
        self.center_frame.setMinimumWidth(300)

        # 优化右侧面板
        self.right_frame.setMinimumWidth(350)
        self.right_frame.setMaximumWidth(400)

        # 优化添加材料面板
        self.add_material_view.setMinimumWidth(350)
        self.add_material_view.setMaximumWidth(400)

        # 设置主布局的拉伸因子
        main_layout = self.ui.findChild(QHBoxLayout, "main_layout")
        if main_layout:
            main_layout.setStretchFactor(self.left_frame, 0)      # 固定宽度
            main_layout.setStretchFactor(self.center_frame, 1)    # 可伸缩
            main_layout.setStretchFactor(self.right_frame, 0)     # 固定宽度
            main_layout.setStretchFactor(self.add_material_view, 0)  # 固定宽度

        # 优化按钮尺寸
        button_height = 48
        self.db_button.setMinimumHeight(button_height)
        self.favorites_button.setMinimumHeight(button_height)
        self.add_material_button.setMinimumHeight(button_height)
        self.modify_button.setMinimumHeight(button_height)
        self.confirm_modify_button.setMinimumHeight(button_height)
        self.confirm_add_button.setMinimumHeight(button_height)

    def resizeEvent(self, event):
        """窗口大小变化事件处理"""
        super().resizeEvent(event)
        self.adjust_responsive_layout()

    def adjust_responsive_layout(self):
        """根据窗口大小调整布局"""
        window_width = self.width()

        # 小屏幕适配（宽度小于1200px）
        if window_width < 1200:
            # 缩小左侧面板
            self.left_frame.setFixedWidth(160)
            # 调整右侧面板
            self.right_frame.setMaximumWidth(320)
            self.add_material_view.setMaximumWidth(320)
        # 中等屏幕适配（宽度1200-1600px）
        elif window_width < 1600:
            self.left_frame.setFixedWidth(180)
            self.right_frame.setMaximumWidth(360)
            self.add_material_view.setMaximumWidth(360)
        # 大屏幕适配（宽度大于1600px）
        else:
            self.left_frame.setFixedWidth(220)
            self.right_frame.setMaximumWidth(420)
            self.add_material_view.setMaximumWidth(420)

        # 超小屏幕处理（宽度小于1000px）
        if window_width < 1000:
            # 可以考虑隐藏某些元素或调整布局
            pass

    def connect_signals(self):
        """连接信号槽"""
        # 按钮点击
        self.db_button.clicked.connect(self.show_material_database)
        self.favorites_button.clicked.connect(self.show_favorites)
        self.add_material_button.clicked.connect(self.show_add_material_view)
        self.modify_button.clicked.connect(self.start_modify_material)
        self.confirm_modify_button.clicked.connect(self.confirm_modify_material)
        self.confirm_add_button.clicked.connect(self.confirm_add_material)

        # 列表选择
        self.materials_list.currentItemChanged.connect(self.on_material_selected)

        # 添加实时验证
        self.setup_form_validation()

    def load_materials(self):
        """加载材料到列表，使用改进的卡片式设计"""
        # 重置过滤状态
        if hasattr(self, 'search_input'):
            self.search_input.clear()
        self.filtered_materials = self.materials.copy()

        # 使用过滤后的材料加载方法
        self.load_filtered_materials()

    def create_material_item_widget(self, material):
        """创建材料项的自定义控件"""
        widget = QWidget()
        widget.setStyleSheet(f"""
            QWidget {{
                background: {Colors.SURFACE};
                border: 1px solid {Colors.BORDER_LIGHT};
                border-radius: 8px;
                margin: 2px;
            }}
            QWidget:hover {{
                background: {Colors.SURFACE_VARIANT};
                border-color: {Colors.PRIMARY_LIGHT};
            }}
        """)

        # 主布局
        main_layout = QHBoxLayout(widget)
        main_layout.setContentsMargins(Spacing.MD, Spacing.SM, Spacing.MD, Spacing.SM)
        main_layout.setSpacing(Spacing.SM)

        # 左侧：材料图标和信息
        left_layout = QVBoxLayout()
        left_layout.setSpacing(2)

        # 材料名称和图标
        name_layout = QHBoxLayout()
        name_layout.setSpacing(Spacing.XS)

        # 材料图标
        material_icon = QLabel(MaterialIcon.get_material_icon(material["name"]))
        material_icon.setFont(Fonts.get_font(16))
        material_icon.setFixedSize(20, 20)

        # 材料名称
        name_label = QLabel(material["name"])
        name_label.setFont(Fonts.get_font(14, bold=True))
        name_label.setStyleSheet(f"color: {Colors.TEXT_PRIMARY};")

        name_layout.addWidget(material_icon)
        name_layout.addWidget(name_label)
        name_layout.addStretch()

        # 材料属性简要信息
        info_label = QLabel(f"密度: {material['density']} kg/m³ | 杨氏模量: {material['youngs_modulus']} GPa")
        info_label.setFont(Fonts.get_font(11))
        info_label.setStyleSheet(f"color: {Colors.TEXT_SECONDARY};")

        left_layout.addLayout(name_layout)
        left_layout.addWidget(info_label)

        # 右侧：收藏按钮
        star_button = CustomStarButton(material["is_favorite"], 28)
        star_button.on_toggle = lambda state, mat=material: self.toggle_favorite(mat, state)

        main_layout.addLayout(left_layout, 1)  # 左侧占用大部分空间
        main_layout.addWidget(star_button, 0)  # 右侧固定宽度

        return widget

    def show_message(self, title, message, msg_type="info"):
        """显示改进的消息提示"""
        # 根据消息类型选择图标和颜色
        icons = {
            "info": "ℹ️",
            "success": "✅",
            "warning": "⚠️",
            "error": "❌"
        }

        icon = icons.get(msg_type, "ℹ️")

        # 创建自定义消息框
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle(f"{icon} {title}")
        msg_box.setText(message)

        # 设置样式
        msg_box.setStyleSheet(f"""
            QMessageBox {{
                background: {Colors.SURFACE};
                color: {Colors.TEXT_PRIMARY};
                font-family: {Fonts.FAMILY_PRIMARY};
                font-size: {Fonts.SIZE_MEDIUM}px;
            }}
            QMessageBox QPushButton {{
                {StyleSheets.primary_button("small")}
                min-width: 80px;
                margin: 4px;
            }}
        """)

        # 根据类型设置按钮
        if msg_type == "error":
            msg_box.setIcon(QMessageBox.Critical)
        elif msg_type == "warning":
            msg_box.setIcon(QMessageBox.Warning)
        elif msg_type == "success":
            msg_box.setIcon(QMessageBox.Information)
        else:
            msg_box.setIcon(QMessageBox.Information)

        msg_box.exec()

    def setup_form_validation(self):
        """设置表单实时验证"""
        # 为添加材料表单设置验证
        self.new_name_input.textChanged.connect(self.validate_name_input)
        self.new_density_input.textChanged.connect(self.validate_density_input)
        self.new_modulus_input.textChanged.connect(self.validate_modulus_input)
        self.new_poisson_input.textChanged.connect(self.validate_poisson_input)

        # 为编辑表单设置验证
        self.density_input.textChanged.connect(self.validate_density_input)
        self.modulus_input.textChanged.connect(self.validate_modulus_input)
        self.poisson_input.textChanged.connect(self.validate_poisson_input)

    def validate_name_input(self, text):
        """验证材料名称输入"""
        if not text.strip():
            self.set_input_error(self.new_name_input, "材料名称不能为空")
        elif any(mat["name"] == text.strip() for mat in self.materials):
            self.set_input_error(self.new_name_input, "材料名称已存在")
        else:
            self.clear_input_error(self.new_name_input)

    def validate_density_input(self, text):
        """验证密度输入"""
        sender = self.sender()
        if not text.strip():
            self.set_input_error(sender, "密度不能为空")
        else:
            try:
                value = float(text)
                if value <= 0:
                    self.set_input_error(sender, "密度必须大于0")
                else:
                    self.clear_input_error(sender)
            except ValueError:
                self.set_input_error(sender, "密度必须是数字")

    def validate_modulus_input(self, text):
        """验证杨氏模量输入"""
        sender = self.sender()
        if not text.strip():
            self.set_input_error(sender, "杨氏模量不能为空")
        else:
            try:
                value = float(text)
                if value <= 0:
                    self.set_input_error(sender, "杨氏模量必须大于0")
                else:
                    self.clear_input_error(sender)
            except ValueError:
                self.set_input_error(sender, "杨氏模量必须是数字")

    def validate_poisson_input(self, text):
        """验证泊松比输入"""
        sender = self.sender()
        if not text.strip():
            self.set_input_error(sender, "泊松比不能为空")
        else:
            try:
                value = float(text)
                if not (0 <= value <= 0.5):
                    self.set_input_error(sender, "泊松比应在0-0.5之间")
                else:
                    self.clear_input_error(sender)
            except ValueError:
                self.set_input_error(sender, "泊松比必须是数字")

    def set_input_error(self, input_widget, error_message):
        """设置输入框错误状态"""
        input_widget.setStyleSheet(f"""
            QLineEdit {{
                background: {Colors.SURFACE};
                border: 2px solid {Colors.ERROR};
                border-radius: 8px;
                padding: 8px 16px;
                font-family: {Fonts.FAMILY_PRIMARY};
                font-size: {Fonts.SIZE_MEDIUM}px;
                color: {Colors.TEXT_PRIMARY};
            }}
            QLineEdit:focus {{
                border-color: {Colors.ERROR};
            }}
        """)
        input_widget.setToolTip(f"❌ {error_message}")

    def clear_input_error(self, input_widget):
        """清除输入框错误状态"""
        input_widget.setStyleSheet(StyleSheets.input_field())
        input_widget.setToolTip("")

    def setup_search_functionality(self):
        """设置搜索功能"""
        # 创建搜索框
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("🔍 搜索材料...")
        self.search_input.setStyleSheet(StyleSheets.input_field())
        self.search_input.textChanged.connect(self.filter_materials)

        # 将搜索框添加到中间面板的布局中
        center_layout = self.center_frame.layout()
        if center_layout:
            # 在标题和列表之间插入搜索框
            center_layout.insertWidget(1, self.search_input)

        # 初始化过滤状态
        self.filtered_materials = self.materials.copy()

    def filter_materials(self, search_text):
        """根据搜索文本过滤材料"""
        search_text = search_text.lower().strip()

        if not search_text:
            # 如果搜索框为空，显示所有材料
            self.filtered_materials = self.materials.copy()
        else:
            # 根据材料名称、密度、杨氏模量等属性进行搜索
            self.filtered_materials = []
            for material in self.materials:
                if (search_text in material["name"].lower() or
                    search_text in material["density"] or
                    search_text in material["youngs_modulus"] or
                    search_text in material["poissons_ratio"]):
                    self.filtered_materials.append(material)

        # 重新加载材料列表
        self.load_filtered_materials()

    def load_filtered_materials(self):
        """加载过滤后的材料列表"""
        self.materials_list.clear()

        # 根据当前模式和搜索结果筛选材料
        if self.current_mode == 1:  # 喜好模式
            materials_to_show = [mat for mat in self.filtered_materials if mat["is_favorite"]]
            self.column_title.setText("🤍 喜好材料")
        else:
            materials_to_show = self.filtered_materials
            self.column_title.setText("🗃️ 材料列表")

        # 如果没有搜索结果，显示提示
        if not materials_to_show and self.search_input.text().strip():
            self.show_no_results_message()
            return

        for material in materials_to_show:
            item = QListWidgetItem()
            item.setSizeHint(QSize(0, 70))
            self.materials_list.addItem(item)

            # 创建材料项的自定义控件
            widget = self.create_material_item_widget(material)
            self.materials_list.setItemWidget(item, widget)
            item.setData(Qt.UserRole, material)

    def show_no_results_message(self):
        """显示无搜索结果的提示"""
        item = QListWidgetItem()
        item.setSizeHint(QSize(0, 100))
        self.materials_list.addItem(item)

        # 创建无结果提示控件
        widget = QWidget()
        widget.setStyleSheet(f"""
            QWidget {{
                background: {Colors.SURFACE_VARIANT};
                border: 1px dashed {Colors.BORDER};
                border-radius: 8px;
                margin: 10px;
            }}
        """)

        layout = QVBoxLayout(widget)
        layout.setAlignment(Qt.AlignCenter)

        # 图标
        icon_label = QLabel("🔍")
        icon_label.setFont(Fonts.get_font(24))
        icon_label.setAlignment(Qt.AlignCenter)

        # 提示文字
        text_label = QLabel("未找到匹配的材料")
        text_label.setFont(Fonts.get_font(14))
        text_label.setStyleSheet(f"color: {Colors.TEXT_SECONDARY};")
        text_label.setAlignment(Qt.AlignCenter)

        # 建议文字
        suggestion_label = QLabel("尝试使用不同的关键词搜索")
        suggestion_label.setFont(Fonts.get_font(12))
        suggestion_label.setStyleSheet(f"color: {Colors.TEXT_SECONDARY};")
        suggestion_label.setAlignment(Qt.AlignCenter)

        layout.addWidget(icon_label)
        layout.addWidget(text_label)
        layout.addWidget(suggestion_label)

        self.materials_list.setItemWidget(item, widget)

    def toggle_favorite(self, material, state):
        """切换材料喜好状态"""
        material["is_favorite"] = state
        if self.current_mode == 1:  # 如果在喜好模式下，重新加载列表
            self.load_materials()

    def show_material_database(self):
        """显示材料库"""
        self.current_mode = 0

        # 如果添加材料视图是显示的，使用动画切换
        if self.add_material_view.isVisible():
            ViewSwitchAnimation.switch_views(self.add_material_view, self.center_frame)
            self.right_frame.show()
        else:
            self.center_frame.show()
            self.right_frame.show()
            self.add_material_view.hide()

        self.load_materials()

    def show_favorites(self):
        """显示喜好材料"""
        self.current_mode = 1

        # 如果添加材料视图是显示的，使用动画切换
        if self.add_material_view.isVisible():
            ViewSwitchAnimation.switch_views(self.add_material_view, self.center_frame)
            self.right_frame.show()
        else:
            self.center_frame.show()
            self.right_frame.show()
            self.add_material_view.hide()

        self.load_materials()

    def show_add_material_view(self):
        """显示添加材料视图"""
        # 使用动画切换视图
        ViewSwitchAnimation.switch_views(self.center_frame, self.add_material_view)
        self.right_frame.hide()

        # 清空输入框
        self.new_name_input.clear()
        self.new_density_input.clear()
        self.new_modulus_input.clear()
        self.new_poisson_input.clear()

    def on_material_selected(self, current, previous):
        """当选择材料时显示其属性"""
        if current is None:
            return

        material = current.data(Qt.UserRole)
        if material:
            self.display_material_properties(material)

    def display_material_properties(self, material):
        """显示材料属性"""
        self.density_input.setText(material["density"])
        self.modulus_input.setText(material["youngs_modulus"])
        self.poisson_input.setText(material["poissons_ratio"])

        # 设置只读状态
        self.set_properties_editable(False)

    def set_properties_editable(self, editable):
        """设置属性输入框是否可编辑"""
        self.density_input.setReadOnly(not editable)
        self.modulus_input.setReadOnly(not editable)
        self.poisson_input.setReadOnly(not editable)

        # 使用新的样式系统
        if editable:
            # 可编辑状态使用标准输入框样式
            input_style = StyleSheets.input_field()
        else:
            # 只读状态使用禁用样式
            input_style = StyleSheets.input_field().replace(
                f"background: {Colors.SURFACE};",
                f"background: {Colors.SURFACE_VARIANT};"
            ).replace(
                f"color: {Colors.TEXT_PRIMARY};",
                f"color: {Colors.TEXT_SECONDARY};"
            )

        self.density_input.setStyleSheet(input_style)
        self.modulus_input.setStyleSheet(input_style)
        self.poisson_input.setStyleSheet(input_style)

    def start_modify_material(self):
        """开始修改材料属性"""
        if not self.materials_list.currentItem():
            self.show_message("警告", "请先选择一个材料！", "warning")
            return

        self.set_properties_editable(True)
        self.modify_button.hide()
        self.confirm_modify_button.show()
        self.editing_mode = True

    def confirm_modify_material(self):
        """确认修改材料属性"""
        # 获取当前选中的材料
        current_item = self.materials_list.currentItem()
        if not current_item:
            self.show_message("警告", "请先选择一个材料！", "warning")
            return

        material = current_item.data(Qt.UserRole)
        if not material:
            return

        # 验证输入
        density = self.density_input.text()
        modulus = self.modulus_input.text()
        poisson = self.poisson_input.text()

        if not density or not modulus or not poisson:
            self.show_message("警告", "所有属性都必须填写！", "warning")
            return

        try:
            float(density)
            float(modulus)
            float(poisson)
        except ValueError:
            self.show_message("错误", "密度、杨氏模量和泊松比必须是数字！", "error")
            return

        # 更新材料
        material["density"] = density
        material["youngs_modulus"] = modulus
        material["poissons_ratio"] = poisson

        # 恢复状态
        self.set_properties_editable(False)
        self.confirm_modify_button.hide()
        self.modify_button.show()
        self.editing_mode = False

        self.show_message("成功", "材料属性修改成功！", "success")

    def confirm_add_material(self):
        """确认添加新材料"""
        name = self.new_name_input.text().strip()
        density = self.new_density_input.text().strip()
        modulus = self.new_modulus_input.text().strip()
        poisson = self.new_poisson_input.text().strip()

        if not name or not density or not modulus or not poisson:
            self.show_message("警告", "所有字段都必须填写！", "warning")
            return

        # 检查名称是否重复
        if any(mat["name"] == name for mat in self.materials):
            self.show_message("警告", "材料名称已存在！", "warning")
            return

        try:
            float(density)
            float(modulus)
            float(poisson)
        except ValueError:
            self.show_message("错误", "密度、杨氏模量和泊松比必须是数字！", "error")
            return

        # 创建新材料，并添加到喜好
        new_material = {
            "name": name,
            "density": density,
            "youngs_modulus": modulus,
            "poissons_ratio": poisson,
            "is_favorite": True  # 默认添加到喜好
        }
        self.materials.append(new_material)

        # 切换回材料库视图
        self.show_material_database()

        # 加载材料并选中新添加的材料
        self.load_materials()

        # 使用动画效果选中新添加的材料
        self.select_material_by_name(name)

        self.show_message("成功", f"材料 '{name}' 添加成功！", "success")

    def select_material_by_name(self, name):
        """根据名称选中材料"""
        for i in range(self.materials_list.count()):
            item = self.materials_list.item(i)
            if item and item.data(Qt.UserRole) and item.data(Qt.UserRole)["name"] == name:
                self.materials_list.setCurrentRow(i)
                # 确保选中的项可见
                self.materials_list.scrollToItem(item)
                break


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    window = MaterialDatabaseApp()
    window.show()
    sys.exit(app.exec())