import sys
import math
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QPushButton, QListWidget, QScrollArea, QLabel, QLineEdit,
    QListWidgetItem, QFrame, QSizePolicy, QMessageBox)
from PySide6.QtUiTools import QUiLoader
from PySide6.QtCore import Qt, QSize, QPointF, QFile
from PySide6.QtGui import QFont, QColor, QPainter, QPen, QBrush


class StarButton(QPushButton):
    """自定义五角星按钮"""

    def __init__(self, is_favorite=False, parent=None):
        super().__init__(parent)
        self.setFixedSize(24, 24)
        self.setFlat(True)
        self.is_favorite = is_favorite
        self.clicked.connect(self.toggle_favorite)

    def toggle_favorite(self):
        self.is_favorite = not self.is_favorite
        self.update()
        # 触发喜好状态改变
        if hasattr(self, 'on_toggle'):
            self.on_toggle(self.is_favorite)

    def paintEvent(self, event):
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)

        # 绘制五角星
        size = min(self.width(), self.height()) - 4
        center_x = self.width() / 2
        center_y = self.height() / 2

        if self.is_favorite:
            painter.setBrush(QBrush(QColor(255, 215, 0)))  # 金色填充
            painter.setPen(QPen(QColor(218, 165, 32), 1))  # 金色边框
        else:
            painter.setBrush(Qt.NoBrush)
            painter.setPen(QPen(QColor(150, 150, 150), 1))  # 灰色边框

        # 绘制五角星
        points = []
        for i in range(5):
            # 外点
            angle = math.pi / 2 + i * 2 * math.pi / 5
            outer_x = center_x + size / 2 * math.cos(angle)
            outer_y = center_y - size / 2 * math.sin(angle)
            points.append((outer_x, outer_y))

            # 内点
            inner_angle = math.pi / 2 + (i + 0.5) * 2 * math.pi / 5
            inner_x = center_x + size / 4 * math.cos(inner_angle)
            inner_y = center_y - size / 4 * math.sin(inner_angle)
            points.append((inner_x, inner_y))

        # 绘制五角星路径
        star_points = [QPointF(x, y) for x, y in points]
        painter.drawPolygon(star_points)


class MaterialDatabaseApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("材料数据库管理系统")
        self.setGeometry(100, 100, 1200, 700)

        # 加载UI文件
        self.load_ui()

        # 初始化材料数据
        self.materials = [
            {"name": "钢材", "density": "7850", "youngs_modulus": "200", "poissons_ratio": "0.3", "is_favorite": True},
            {"name": "铝合金", "density": "2700", "youngs_modulus": "70", "poissons_ratio": "0.33",
             "is_favorite": False},
            {"name": "铜", "density": "8960", "youngs_modulus": "110", "poissons_ratio": "0.34", "is_favorite": False},
            {"name": "钛合金", "density": "4500", "youngs_modulus": "116", "poissons_ratio": "0.32",
             "is_favorite": False},
            {"name": "镁合金", "density": "1740", "youngs_modulus": "45", "poissons_ratio": "0.35",
             "is_favorite": False},
            {"name": "镍合金", "density": "8900", "youngs_modulus": "200", "poissons_ratio": "0.31",
             "is_favorite": False},
            {"name": "陶瓷", "density": "3900", "youngs_modulus": "350", "poissons_ratio": "0.22",
             "is_favorite": False},
            {"name": "玻璃", "density": "2500", "youngs_modulus": "70", "poissons_ratio": "0.23", "is_favorite": False},
            {"name": "橡胶", "density": "1100", "youngs_modulus": "0.01", "poissons_ratio": "0.49",
             "is_favorite": False},
            {"name": "木材", "density": "600", "youngs_modulus": "10", "poissons_ratio": "0.3", "is_favorite": False},
        ]

        # 当前显示模式：0-材料库，1-喜好
        self.current_mode = 0
        # 当前编辑状态
        self.editing_mode = False

        # 设置样式
        self.set_styles()

        # 连接信号
        self.connect_signals()

        # 加载材料到列表
        self.load_materials()

        # 默认显示第一个材料
        if self.materials:
            self.materials_list.setCurrentRow(0)
            self.display_material_properties(self.materials[0])

        # 隐藏确认修改按钮
        self.confirm_modify_button.hide()

        # 设置添加材料视图初始状态
        self.add_material_view.hide()

    def load_ui(self):
        """加载UI文件"""
        ui_file = QFile(r"D:\Projects\2025\2025.7\Auto Mechanical\self_Pyside6\ui_windows\material_database.ui")
        if not ui_file.open(QFile.ReadOnly):
            print(f"Cannot open {ui_file.fileName()}: {ui_file.errorString()}")
            sys.exit(-1)

        loader = QUiLoader()
        self.ui = loader.load(ui_file)
        ui_file.close()

        if not self.ui:
            print(loader.errorString())
            sys.exit(-1)

        self.setCentralWidget(self.ui)

        # 获取UI中的控件
        self.left_frame = self.ui.findChild(QFrame, "left_frame")
        self.db_button = self.ui.findChild(QPushButton, "db_button")
        self.favorites_button = self.ui.findChild(QPushButton, "favorites_button")
        self.add_material_button = self.ui.findChild(QPushButton, "add_material_button")

        self.center_frame = self.ui.findChild(QFrame, "center_frame")
        self.column_title = self.ui.findChild(QLabel, "column_title")
        self.scrollArea = self.ui.findChild(QScrollArea, "scrollArea")
        self.materials_list = self.ui.findChild(QListWidget, "materials_list")

        self.right_frame = self.ui.findChild(QFrame, "right_frame")
        self.density_input = self.ui.findChild(QLineEdit, "density_input")
        self.modulus_input = self.ui.findChild(QLineEdit, "modulus_input")
        self.poisson_input = self.ui.findChild(QLineEdit, "poisson_input")
        self.modify_button = self.ui.findChild(QPushButton, "modify_button")
        self.confirm_modify_button = self.ui.findChild(QPushButton, "confirm_modify_button")

        self.add_material_view = self.ui.findChild(QFrame, "add_material_view")
        self.new_name_input = self.ui.findChild(QLineEdit, "new_name_input")
        self.new_density_input = self.ui.findChild(QLineEdit, "new_density_input")
        self.new_modulus_input = self.ui.findChild(QLineEdit, "new_modulus_input")
        self.new_poisson_input = self.ui.findChild(QLineEdit, "new_poisson_input")
        self.confirm_add_button = self.ui.findChild(QPushButton, "confirm_add_button")

    def set_styles(self):
        """设置控件样式"""
        # 输入框样式
        input_style = """
            QLineEdit {
                padding: 10px;
                border: 1px solid #ccc;
                border-radius: 6px;
                font-size: 14px;
                background-color: #f8f8f8;
            }
            QLineEdit:focus {
                border: 2px solid #4CAF50;
                background-color: white;
            }
        """
        self.density_input.setStyleSheet(input_style)
        self.modulus_input.setStyleSheet(input_style)
        self.poisson_input.setStyleSheet(input_style)
        self.new_name_input.setStyleSheet(input_style)
        self.new_density_input.setStyleSheet(input_style)
        self.new_modulus_input.setStyleSheet(input_style)
        self.new_poisson_input.setStyleSheet(input_style)

    def connect_signals(self):
        """连接信号槽"""
        # 按钮点击
        self.db_button.clicked.connect(self.show_material_database)
        self.favorites_button.clicked.connect(self.show_favorites)
        self.add_material_button.clicked.connect(self.show_add_material_view)
        self.modify_button.clicked.connect(self.start_modify_material)
        self.confirm_modify_button.clicked.connect(self.confirm_modify_material)
        self.confirm_add_button.clicked.connect(self.confirm_add_material)

        # 列表选择
        self.materials_list.currentItemChanged.connect(self.on_material_selected)

    def load_materials(self):
        """加载材料到列表，修复名称重叠问题"""
        self.materials_list.clear()

        # 根据当前模式筛选材料
        materials_to_show = self.materials
        if self.current_mode == 1:  # 喜好模式
            materials_to_show = [mat for mat in self.materials if mat["is_favorite"]]
            self.column_title.setText("喜好材料")
        else:
            self.column_title.setText("材料列表")

        for material in materials_to_show:
            item = QListWidgetItem()
            item.setSizeHint(QSize(0, 50))  # 设置项的高度
            self.materials_list.addItem(item)

            # 为每个材料项创建自定义控件
            widget = QWidget()

            # 修复重叠问题：使用HBoxLayout，并设置名称标签的伸缩策略
            layout = QHBoxLayout(widget)
            layout.setContentsMargins(5, 5, 5, 5)

            # 材料名称 - 设置伸缩策略
            name_label = QLabel(material["name"])
            name_font = QFont("Arial", 12)
            name_label.setFont(name_font)
            name_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)  # 关键修复：名称标签占用所有可用空间

            # 五角星按钮
            star_button = StarButton(material["is_favorite"])
            star_button.on_toggle = lambda state, mat=material: self.toggle_favorite(mat, state)

            layout.addWidget(name_label)
            layout.addWidget(star_button)

            self.materials_list.setItemWidget(item, widget)
            item.setData(Qt.UserRole, material)  # 存储材料数据

    def toggle_favorite(self, material, state):
        """切换材料喜好状态"""
        material["is_favorite"] = state
        if self.current_mode == 1:  # 如果在喜好模式下，重新加载列表
            self.load_materials()

    def show_material_database(self):
        """显示材料库"""
        self.current_mode = 0
        self.center_frame.show()
        self.right_frame.show()
        self.add_material_view.hide()
        self.load_materials()

    def show_favorites(self):
        """显示喜好材料"""
        self.current_mode = 1
        self.center_frame.show()
        self.right_frame.show()
        self.add_material_view.hide()
        self.load_materials()

    def show_add_material_view(self):
        """显示添加材料视图"""
        self.center_frame.hide()
        self.right_frame.hide()
        self.add_material_view.show()
        # 清空输入框
        self.new_name_input.clear()
        self.new_density_input.clear()
        self.new_modulus_input.clear()
        self.new_poisson_input.clear()

    def on_material_selected(self, current, previous):
        """当选择材料时显示其属性"""
        if current is None:
            return

        material = current.data(Qt.UserRole)
        if material:
            self.display_material_properties(material)

    def display_material_properties(self, material):
        """显示材料属性"""
        self.density_input.setText(material["density"])
        self.modulus_input.setText(material["youngs_modulus"])
        self.poisson_input.setText(material["poissons_ratio"])

        # 设置只读状态
        self.set_properties_editable(False)

    def set_properties_editable(self, editable):
        """设置属性输入框是否可编辑"""
        self.density_input.setReadOnly(not editable)
        self.modulus_input.setReadOnly(not editable)
        self.poisson_input.setReadOnly(not editable)

        # 根据编辑状态设置背景色
        bg_color = "#ffffff" if editable else "#f8f8f8"
        self.density_input.setStyleSheet(f"""
            QLineEdit {{
                padding: 10px;
                border: 1px solid #ccc;
                border-radius: 6px;
                font-size: 14px;
                background-color: {bg_color};
            }}
            QLineEdit:focus {{
                border: 2px solid #4CAF50;
            }}
        """)
        self.modulus_input.setStyleSheet(self.density_input.styleSheet())
        self.poisson_input.setStyleSheet(self.density_input.styleSheet())

    def start_modify_material(self):
        """开始修改材料属性"""
        if not self.materials_list.currentItem():
            QMessageBox.warning(self, "警告", "请先选择一个材料！")
            return

        self.set_properties_editable(True)
        self.modify_button.hide()
        self.confirm_modify_button.show()
        self.editing_mode = True

    def confirm_modify_material(self):
        """确认修改材料属性"""
        # 获取当前选中的材料
        current_item = self.materials_list.currentItem()
        if not current_item:
            QMessageBox.warning(self, "警告", "请先选择一个材料！")
            return

        material = current_item.data(Qt.UserRole)
        if not material:
            return

        # 验证输入
        density = self.density_input.text()
        modulus = self.modulus_input.text()
        poisson = self.poisson_input.text()

        if not density or not modulus or not poisson:
            QMessageBox.warning(self, "警告", "所有属性都必须填写！")
            return

        try:
            float(density)
            float(modulus)
            float(poisson)
        except ValueError:
            QMessageBox.warning(self, "警告", "密度、杨氏模量和泊松比必须是数字！")
            return

        # 更新材料
        material["density"] = density
        material["youngs_modulus"] = modulus
        material["poissons_ratio"] = poisson

        # 恢复状态
        self.set_properties_editable(False)
        self.confirm_modify_button.hide()
        self.modify_button.show()
        self.editing_mode = False

        QMessageBox.information(self, "成功", "材料属性修改成功！")

    def confirm_add_material(self):
        """确认添加新材料"""
        name = self.new_name_input.text().strip()
        density = self.new_density_input.text().strip()
        modulus = self.new_modulus_input.text().strip()
        poisson = self.new_poisson_input.text().strip()

        if not name or not density or not modulus or not poisson:
            QMessageBox.warning(self, "警告", "所有字段都必须填写！")
            return

        # 检查名称是否重复
        if any(mat["name"] == name for mat in self.materials):
            QMessageBox.warning(self, "警告", "材料名称已存在！")
            return

        try:
            float(density)
            float(modulus)
            float(poisson)
        except ValueError:
            QMessageBox.warning(self, "警告", "密度、杨氏模量和泊松比必须是数字！")
            return

        # 创建新材料，并添加到喜好
        new_material = {
            "name": name,
            "density": density,
            "youngs_modulus": modulus,
            "poissons_ratio": poisson,
            "is_favorite": True  # 默认添加到喜好
        }
        self.materials.append(new_material)

        # 切换回材料库视图
        self.show_material_database()

        # 加载材料并选中新添加的材料
        self.load_materials()
        for i in range(self.materials_list.count()):
            item = self.materials_list.item(i)
            if item.data(Qt.UserRole)["name"] == name:
                self.materials_list.setCurrentRow(i)
                break

        QMessageBox.information(self, "成功", "新材料添加成功！")


if __name__ == "__main__":
    app = QApplication(sys.argv)
    app.setStyle("Fusion")
    window = MaterialDatabaseApp()
    window.show()
    sys.exit(app.exec())